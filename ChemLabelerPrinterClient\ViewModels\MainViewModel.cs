
using ChemLabelerPrinterClient.Models;
using ChemLabelerPrinterClient.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;

namespace ChemLabelerPrinterClient.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly IFileService _fileService;
        private readonly ILabelParserService _labelParserService;
        private readonly Dictionary<string, IPrintingService> _printingServices;

        private LabelData _labelData;
        public ObservableCollection<VariableViewModel> Variables { get; } = new ObservableCollection<VariableViewModel>();

        private string _selectedPrintService = "WPF Visual";
        public string SelectedPrintService
        {
            get => _selectedPrintService;
            set
            {
                _selectedPrintService = value;
                OnPropertyChanged(nameof(SelectedPrintService));
            }
        }

        public List<string> PrintServices { get; }

        private int _printQuantity = 1;
        public int PrintQuantity
        {
            get => _printQuantity;
            set
            {
                _printQuantity = value;
                OnPropertyChanged(nameof(PrintQuantity));
            }
        }

        public ICommand PrintCommand { get; }

        public MainViewModel()
        {
            _fileService = new FileService();
            _labelParserService = new LabelParserService();
            _printingServices = new Dictionary<string, IPrintingService>
            {
                { "WPF Visual", new WpfVisualPrintingService() },
                { "ZPL", new ZplPrintingService() }
            };
            PrintServices = _printingServices.Keys.ToList();

            PrintCommand = new RelayCommand(async () => await Print());

            LoadLabelData();
        }

        private async Task LoadLabelData()
        {
            _labelData = await _fileService.ReadLabelDataAsync(@"D:\Codes\Temp\ChemLabelerPrinter\ChemLabelerPrinterClient\label.json");
            var variableNames = _labelParserService.ExtractVariables(_labelData.CanvasContent);
            foreach (var name in variableNames.Distinct())
            {
                Variables.Add(new VariableViewModel { Name = name });
            }
        }

        private async Task Print()
        {
            var variableValues = Variables.ToDictionary(v => v.Name, v => v.Value);
            var elements = _labelParserService.ParseCanvasContent(_labelData.CanvasContent, variableValues);
            _printingServices[SelectedPrintService].Print(_labelData.LabelSpecification, elements, PrintQuantity);
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class VariableViewModel : INotifyPropertyChanged
    {
        private string _name;
        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        private string _value;
        public string Value
        {
            get => _value;
            set
            {
                _value = value;
                OnPropertyChanged(nameof(Value));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Func<Task> _execute;
        public event System.EventHandler CanExecuteChanged;

        public RelayCommand(Func<Task> execute)
        {
            _execute = execute;
        }

        public bool CanExecute(object parameter) => true;

        public async void Execute(object parameter)
        {
            await _execute();
        }
    }
}

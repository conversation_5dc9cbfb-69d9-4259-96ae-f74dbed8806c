# 化学品标签打印客户端

## 需求
做一个Windows的标签打印客户端，主要打印化学品的标签，使用标签打印机。对于打印质量有一定要求，因为标签较小。
标签的内容除了文字，还会有图片，二维码或条型码等。
标签设计是基于canvas画布设计的，存储的也是画布的元素信息，在打印时需要去替换变量值，详细见 ./label.json 文件的 CanvasContent 字段。
纸的定义需要在每次打印任务时去设置打印机参数，比如纸张大小，边距等,详细见 ./label.json 文件的 44-50行的属性。
会存在批量打印，比如一次打印几十到几百张

## 方案
基于这个需求，决定采用直接与打印机通信 (Direct Communication)
原理：绕过Windows的图形渲染管线，直接生成打印机本身能理解的指令语言（如ZPL, TSPL, EPL）。绝大多数专业标签打印机（如Zebra, TSC, Godex, Postek）都支持这类语言。
优点：
最高质量：文字、条码、二维码都是由打印机固件直接生成的矢量图形，清晰度最高，边缘锐利。
最高速度：指令数据量小，传输和打印速度极快，非常适合批量打印。
精确控制：可以精确控制打印位置（到dot）、打印浓度、速度等所有硬件参数。
字体优势：可以利用打印机内置的高质量矢量字体，无需担心PC上的字体版权和渲染问题。


## 实现
技术栈采用 .net8 + WPF + SQLite3
如果打印机支持 ZPL, TSPL, EPL，则直接通过指令与打印交互，如果不支持，则采用通用打印方案





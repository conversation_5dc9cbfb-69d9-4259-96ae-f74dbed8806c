
using ChemLabelerPrinterClient.Models;
using System.Collections.Generic;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows;
using System.IO;
using System;
using QRCoder;

namespace ChemLabelerPrinterClient.Services
{
    public class WpfVisualPrintingService : IPrintingService
    {
        public void Print(LabelSpecification labelSpec, List<CanvasElement> elements, int quantity)
        {
            var canvas = new Canvas
            {
                Width = labelSpec.Attributes.LabelWidth * 3.7795275591, // mm to px (96 DPI)
                Height = labelSpec.Attributes.LabelLength * 3.7795275591,
                Background = Brushes.White
            };

            foreach (var element in elements)
            {
                UIElement uiElement = null;
                switch (element.Type)
                {
                    case "text":
                        var textElement = element as TextElement;
                        uiElement = new TextBlock
                        {
                            Text = textElement.Content,
                            FontSize = textElement.FontSize,
                            FontFamily = new FontFamily(textElement.FontFamily),
                            FontWeight = (FontWeight)new FontWeightConverter().ConvertFromString(textElement.FontWeight),
                            FontStyle = (FontStyle)new FontStyleConverter().ConvertFromString(textElement.FontStyle),
                            Foreground = (Brush)new BrushConverter().ConvertFromString(textElement.FillColor)
                        };
                        break;
                    case "image":
                        var imageElement = element as ImageElement;
                        var bitmap = new BitmapImage();
                        var base64Data = imageElement.Content.Split(',')[1];
                        using (var stream = new MemoryStream(Convert.FromBase64String(base64Data)))
                        {
                            bitmap.BeginInit();
                            bitmap.StreamSource = stream;
                            bitmap.CacheOption = BitmapCacheOption.OnLoad;
                            bitmap.EndInit();
                        }
                        uiElement = new Image { Source = bitmap, Width = imageElement.Width, Height = imageElement.Height };
                        break;
                    case "qrcode":
                        var qrCodeElement = element as QrCodeElement;
                        var qrGenerator = new QRCodeGenerator();
                        var qrCodeData = qrGenerator.CreateQrCode(qrCodeElement.Content, QRCodeGenerator.ECCLevel.M);
                        var qrCode = new QRCode(qrCodeData);
                        var qrCodeImage = qrCode.GetGraphic(20, qrCodeElement.FillColor, "#FFFFFF", true);
                        using (var stream = new MemoryStream())
                        {
                            qrCodeImage.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
                            stream.Position = 0;
                            var bmp = new BitmapImage();
                            bmp.BeginInit();
                            bmp.StreamSource = stream;
                            bmp.CacheOption = BitmapCacheOption.OnLoad;
                            bmp.EndInit();
                            uiElement = new Image { Source = bmp, Width = qrCodeElement.Size, Height = qrCodeElement.Size };
                        }
                        break;
                }

                if (uiElement != null)
                {
                    Canvas.SetLeft(uiElement, element.X * 3.7795275591);
                    Canvas.SetTop(uiElement, element.Y * 3.7795275591);
                    canvas.Children.Add(uiElement);
                }
            }

            var printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                // A4 paper size in pixels at 96 DPI
                var pageSize = new Size(210 * 3.7795275591, 297 * 3.7795275591);
                canvas.Measure(pageSize);
                canvas.Arrange(new Rect(new Point(0, 0), pageSize));
                printDialog.PrintVisual(canvas, "ChemLabeler");
            }
        }
    }
}
